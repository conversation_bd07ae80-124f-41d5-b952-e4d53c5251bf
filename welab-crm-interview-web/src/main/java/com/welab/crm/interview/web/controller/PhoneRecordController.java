package com.welab.crm.interview.web.controller;

import com.welab.collection.interview.vo.PhoneRecordVO;
import com.welab.common.response.Response;
import com.welab.crm.interview.dto.ConPhoneCallInfoDTO;
import com.welab.crm.interview.dto.QualityInspectionCallbackDTO;
import com.welab.crm.interview.service.ConphoneCallInfoService;
import com.welab.crm.interview.service.QualityInspectionService;
import com.welab.crm.interview.service.TrTokenService;
import com.welab.crm.interview.web.constants.Urls.PhoneRecord;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import javax.annotation.Resource;
import lombok.extern.slf4j.Slf4j;
import org.springframework.web.bind.annotation.*;

import java.util.List;

/**
 * 软电话记录controller
 * <AUTHOR>
 * @date 2021/10/21 13:58
 * @module 客服项目
 */

@Slf4j
@RestController
@RequestMapping(PhoneRecord.V1_PHONE)
@Api(value = "PhoneRecordController", description = "软电话controller")
public class PhoneRecordController {

    @Resource
    private ConphoneCallInfoService callInfoService;
    @Resource
    private TrTokenService trTokenService;
    @Resource
    private QualityInspectionService qualityInspectionService;

    @PostMapping(PhoneRecord.V1_PHONE_CALL_BACK)
    @ApiOperation(value = PhoneRecord.V1_PHONE_CALL_BACK, notes = PhoneRecord.V1_PHONE_CALL_BACK_DESC)
    public Response<String> phoneRecordCallBack(ConPhoneCallInfoDTO conPhoneCallInfoDTO){
        return Response.success(callInfoService.saveConPhoneCallBackInfo(conPhoneCallInfoDTO));
    }

    @GetMapping(PhoneRecord.V1_PHONE_RECORD_QUERY)
    @ApiOperation(value = PhoneRecord.V1_PHONE_RECORD_QUERY_DESC, notes = PhoneRecord.V1_PHONE_RECORD_QUERY_DESC)
    public Response<List<PhoneRecordVO>> phoneRecordQuery(@RequestParam String queryDate){
        return Response.success(callInfoService.queryRecord(queryDate));
    }

    @GetMapping(PhoneRecord.V1_PHONE_REPORT_MANUAL)
    @ApiOperation(value = PhoneRecord.V1_PHONE_REPORT_MANUAL_DESC, notes = PhoneRecord.V1_PHONE_REPORT_MANUAL_DESC)
    public Response<Void> phoneReportManual(@RequestParam String countDate) {
        trTokenService.saveTrunkReportIbToDb(countDate);
        return Response.success();
    }

    @PostMapping(PhoneRecord.V1_PHONE_QUALITY_CALLBACK)
    @ApiOperation(value = PhoneRecord.V1_PHONE_QUALITY_CALLBACK_DESC, notes = PhoneRecord.V1_PHONE_QUALITY_CALLBACK_DESC)
    public Response<String> qualityInspectionCallback(@RequestBody QualityInspectionCallbackDTO callbackDTO) {
        try {
            log.info("接收到质检回调，参数：{}", callbackDTO);
            qualityInspectionService.handleQualityInspectionCallback(callbackDTO);
            return Response.success("success");
        } catch (Exception e) {
            log.error("处理质检回调异常", e);
            return Response.fail("处理失败：" + e.getMessage());
        }
    }


}
