package com.welab.crm.interview.service.impl;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.welab.collection.interview.vo.PhoneRecordVO;
import com.welab.common.utils.http.HttpClientUtil;
import com.welab.common.utils.http.HttpConfig;
import com.welab.common.utils.http.HttpHeader;
import com.welab.crm.interview.bo.ConRepeatCallBO;
import com.welab.crm.interview.constant.BusiConstant;
import com.welab.crm.interview.domain.CallInfoAiSummary;
import com.welab.crm.interview.domain.ConPhoneCallInfo;
import com.welab.crm.interview.domain.InAuthCrmStaff;
import com.welab.crm.interview.domain.IvrSummary;
import com.welab.crm.interview.dto.ConPhoneCallInfoDTO;
import com.welab.crm.interview.mapper.CallInfoAiSummaryMapper;
import com.welab.crm.interview.mapper.ConPhoneCallInfoMapper;
import com.welab.crm.interview.mapper.InAuthCrmStaffMapper;
import com.welab.crm.interview.mapper.IvrSummaryMapper;
import com.welab.crm.interview.service.ConphoneCallInfoService;
import com.welab.crm.interview.service.QualityInspectionService;
import com.welab.crm.interview.service.TrTokenService;
import com.welab.crm.interview.util.DateUtil;

import java.text.ParseException;
import java.text.SimpleDateFormat;
import java.util.Date;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.concurrent.ArrayBlockingQueue;
import java.util.concurrent.CompletableFuture;
import java.util.concurrent.ThreadPoolExecutor;
import java.util.concurrent.TimeUnit;
import java.util.stream.Collectors;
import javax.annotation.Resource;

import com.welab.crm.interview.vo.ai.AiSummaryResVO;
import com.welab.exception.FastRuntimeException;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.apache.http.Header;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;

/**
 * <AUTHOR>
 * @date 2021/10/20 17:48
 */
@Service
@Slf4j
public class ConPhoneCallInfoServiceImpl implements ConphoneCallInfoService {

    @Resource
    private ConPhoneCallInfoMapper conPhoneCallInfoMapper;
    @Resource
    private IvrSummaryMapper ivrSummaryMapper;

    @Resource
    private ConRepeatCallBO conRepeatCallBO;
    
    @Resource
    private TrTokenService trTokenService;
    
    @Resource
    private CallInfoAiSummaryMapper callInfoAiSummaryMapper;
    
    @Resource
    private InAuthCrmStaffMapper inAuthCrmStaffMapper;

    @Resource
    private QualityInspectionService qualityInspectionService;

    @Value("${ai.summary.base.url}")
    private String aiSummaryBaseUrl;

    private static final ThreadPoolExecutor executor = new ThreadPoolExecutor(30, 50,
            300L, TimeUnit.SECONDS, new ArrayBlockingQueue<>(1000), new ThreadPoolExecutor.CallerRunsPolicy());
    
    
    private static final Integer AI_TIME_OUT = 60000;

    @Override
    public String saveConPhoneCallBackInfo(ConPhoneCallInfoDTO dto) {
        log.info("start saveConPhoneCallBackInfo,cdrMainRequestId:{}", dto.getCdr_main_unique_id());
        try {
            ConPhoneCallInfo info = null;
            // 呼入
            if ("1".equals(dto.getCdr_call_type())) {
                info = conPhoneCallInfoMapper.selectOne(Wrappers.lambdaQuery(ConPhoneCallInfo.class)
                        .eq(ConPhoneCallInfo::getCdrMainUniqueId, dto.getCdr_main_unique_id())
                        .eq(StringUtils.isNotBlank(dto.getCdr_callee_cno()), ConPhoneCallInfo::getCdrCalleeCno,
                                dto.getCdr_callee_cno()));
                // 保存ivr按键轨迹
                saveIvrSummary(dto.getCdr_main_unique_id(),dto.getCdr_customer_number(),dto.getCdr_ivr_flow());
            } else {
                info = conPhoneCallInfoMapper.selectOne(Wrappers.lambdaQuery(ConPhoneCallInfo.class)
                    .eq(ConPhoneCallInfo::getCdrMainUniqueId, dto.getCdr_main_unique_id())
                    .eq(StringUtils.isNotBlank(dto.getCdr_cno()), ConPhoneCallInfo::getCdrCno,
                        dto.getCdr_cno()));
            }
            if (Objects.isNull(info)) {
                log.info("ConPhoneCallInfo is null");
                info = new ConPhoneCallInfo();
            }
            convertDtoToDomain(dto, info);
            insertOrUpdate(info);
            conRepeatCallBO.addRecord(info);
            if (checkIsNeedAiSummary(info)) {
                CompletableFuture.runAsync(() -> saveAiSummary(dto.getCdr_main_unique_id(), dto.getCdr_record_file_1(), dto.getCdr_call_type()), executor);
            }

            // 异步提交质检任务
            if (checkIsNeedQualityInspection(info, dto.getCdr_record_file_1())) {
                String agentNumber = getCdrCno(info);
                qualityInspectionService.submitPhoneRecordQualityInspection(
                    dto.getCdr_main_unique_id(),
                    dto.getCdr_record_file_1(),
                    dto.getCdr_call_type(),
                    dto.getCdr_customer_number(),
                    agentNumber
                );
            }

            return "success";
        } catch (Exception e) {
            log.error("saveConPhoneCallBackInfo 保存软电话回调异常, {}", dto.toString(), e);
            return "fail";
        }
    }
    
    private boolean checkIsNeedAiSummary(ConPhoneCallInfo info){
        return Objects.nonNull(info.getCdrBridgeTime())
                && StringUtils.isNotBlank(info.getCdrEndBridgeTime())
                && info.getCdrEndBridgeTime().compareTo("00:00:30") > 0;
    }

    /**
     * 检查是否需要质检
     * @param info 通话信息
     * @param recordFile 录音文件
     * @return 是否需要质检
     */
    private boolean checkIsNeedQualityInspection(ConPhoneCallInfo info, String recordFile) {
        // 有录音文件且通话时长超过30秒
        return StringUtils.isNotBlank(recordFile)
                && Objects.nonNull(info.getCdrBridgeTime())
                && StringUtils.isNotBlank(info.getCdrEndBridgeTime())
                && info.getCdrEndBridgeTime().compareTo("00:00:30") > 0;
    }
    

    private void saveAiSummary(String cdrMainUniqueId, String recordFile, String cdrCallType) {
        if (StringUtils.isBlank(recordFile)){
            log.info("{}无通话录音", cdrMainUniqueId);
            return;
        }

        Integer existCount = callInfoAiSummaryMapper.selectCount(Wrappers.lambdaQuery(CallInfoAiSummary.class)
                .eq(CallInfoAiSummary::getCdrMainUniqueId, cdrMainUniqueId));
        if (existCount > 0){
            log.info("{} ai小结已存在", cdrMainUniqueId);
            return;
        }
        try {
            String enterpriseId = recordFile.substring(0, 7);
            String waveSoundUrl = trTokenService.getWaveSoundUrl(enterpriseId, recordFile);
            JSONObject params = new JSONObject();
            params.put("url", waveSoundUrl);
            params.put("type", "4".equals(cdrCallType) ? 2 : 1);
            Header[] headers = HttpHeader.custom().contentType(HttpHeader.Headers.APPLICATION_JSON).build();
            HttpConfig httpConfig = HttpConfig.custom()
                    .url(aiSummaryBaseUrl + "/llm/v1/call/summarize")
                    .json(JSON.toJSONString(params))
                    .headers(headers)
                    .requestTimeout(AI_TIME_OUT)
                    .connectTimeout(AI_TIME_OUT)
                    .socketTimeout(AI_TIME_OUT);

            log.info("saveAiSummary params:{}", JSON.toJSONString(params));
            String resStr = HttpClientUtil.post(httpConfig);
            log.info("saveAiSummary result:{}", resStr);

            AiSummaryResVO resVO = JSON.parseObject(resStr, AiSummaryResVO.class);
            CallInfoAiSummary aiSummary = new CallInfoAiSummary();
            aiSummary.setCdrMainUniqueId(cdrMainUniqueId);
            aiSummary.setSummary(resVO.getData().getSummary());
            aiSummary.setGmtCreate(new Date());
            aiSummary.setUid(resVO.getUid());
            aiSummary.setDialogue(resVO.getData().getDialogue());

            callInfoAiSummaryMapper.insert(aiSummary);

        } catch (Exception e) {
            log.warn("Error while saving AI summary for record file {}: {}", recordFile, e.getMessage(), e);
        }
    }




    public void saveIvrSummary(String cdrMainUniqueId, String cdrCustomerNumber, String cdrIvrFlow) {
        try {
            log.info("saveIvrSummary 保存ivr按键轨迹params,cdrMainUniqueId:{},cdrIvrFlow:{}",
                    cdrMainUniqueId, cdrIvrFlow);
            if (StringUtils.isNotBlank(cdrIvrFlow)) {
                String[] split = cdrIvrFlow.split(",");
                for (int i = 0; i < split.length; i++) {
                    String ivrStr = split[i];
                    if (StringUtils.isNotBlank(ivrStr)){
                        String[] ivrArray = ivrStr.split("\\|");
                        if (ivrArray.length >= 5) {
                            IvrSummary ivrSummary = new IvrSummary();
                            ivrSummary.setCdrMainUniqueId(cdrMainUniqueId);
                            ivrSummary.setMobile(cdrCustomerNumber);
                            ivrSummary.setPressKey(ivrArray[1]);
                            ivrSummary.setStartTime(timeDeal(ivrArray[3]));
                            ivrSummary.setEndTime(timeDeal(ivrArray[4]));
                            ivrSummaryMapper.insert(ivrSummary);
                        }
                    }
                }

            }
        } catch (Exception e){
            log.error("saveIvrSummary 保存ivr按键轨迹异常", e);
        }
    }

    /**
     * 时间戳解析
     * @param time
     * @return
     */
    private Date timeDeal(String time) {
        log.debug("待解析时间戳time:"+time);
        if (null != time && !"".equals(time)) {
            long timeLong = Long.parseLong(time);
            if(Long.toString(timeLong).length() == 10) {
                log.debug("开始解析时间戳time:" + timeLong);
                return new Date(timeLong* 1000L);
            }
            if(Long.toString(timeLong).length()==13) {
                log.debug("开始解析时间戳time:" + timeLong);
                return new Date(timeLong);
            }
        }
        return null;
    }

    private void convertDtoToDomain(ConPhoneCallInfoDTO dto, ConPhoneCallInfo info) {
        info.setCdrEnterpriseId(dto.getCdr_enterprise_id());
        info.setCdrNumberTrunk(dto.getCdr_number_trunk());
        info.setCdrHotline(dto.getCdr_hotline());
        info.setCdrMainUniqueId(dto.getCdr_main_unique_id());
        info.setCdrCustomerNumber(dto.getCdr_customer_number());
        info.setCdrCustomerAreaCode(dto.getCdr_customer_area_code());
        info.setCdrCustomerCity(dto.getCdr_customer_city());
        info.setCdrCustomerProvince(dto.getCdr_customer_province());
        info.setCdrCustomerNumberType(dto.getCdr_customer_number_type());
        info.setCdrStatus(dto.getCdr_status());
        info.setCdrCallType(dto.getCdr_call_type());
        info.setCdrCalleeCno(dto.getCdr_callee_cno());
        if (StringUtils.isNotBlank(dto.getCdr_join_queue_time())) {
            info.setCdrJoinQueueTime(new Date(Long.parseLong(dto.getCdr_join_queue_time()) * 1000));
        }
        if (StringUtils.isNotBlank(dto.getCdr_bridge_time())) {
            info.setCdrBridgeTime(new Date(Long.parseLong(dto.getCdr_bridge_time()) * 1000));
        }
        if (StringUtils.isNotBlank(dto.getCdr_start_time())) {
            info.setCdrStartTime(new Date(Long.parseLong(dto.getCdr_start_time()) * 1000));
        }
        if (StringUtils.isNotBlank(dto.getCdr_answer_time())) {
            info.setCdrAnswerTime(new Date(Long.parseLong(dto.getCdr_answer_time()) * 1000));
        }
        if (StringUtils.isNotBlank(dto.getCdr_end_time())) {
            info.setCdrEndTime(new Date(Long.parseLong(dto.getCdr_end_time()) * 1000));
        }
        info.setCdrRecordFile(dto.getCdr_record_file_1());
        info.setCdrEndReason(dto.getCdr_end_reason());
        info.setCdrEndBridgeTime(dto.getCdr_end_bridge_time());
        info.setCdrQueue(dto.getCdr_queue());
        info.setCdrDetailSipCause(dto.getCdr_detail_sip_cause());
        info.setCdrCno(dto.getCdr_cno());
        info.setCdrXNumber(dto.getCdr_x_number());
        if (StringUtils.isNotBlank(dto.getCallee_ringing_time())) {
            info.setCalleeRingingTime(new Date(Long.parseLong(dto.getCallee_ringing_time()) * 1000));
        }
        info.setCdrRequestUniqueId(dto.getCdr_request_unique_id());
        info.setCdrAgentNumber(dto.getCdr_agent_number());
        info.setCustCalleeClid(dto.getCust_callee_clid());
        info.setCdrClid(dto.getCdr_clid());
        if (StringUtils.isNotBlank(dto.getCdr_end_time()) && StringUtils.isNotBlank(dto.getCdr_bridge_time())) {
            info.setCdrEndBridgeTime(DateUtil.secondsToTime(
                    Integer.parseInt(dto.getCdr_end_time()) - Integer.parseInt(dto.getCdr_bridge_time())));
        } else {
            info.setCdrEndBridgeTime("0");
        }

        info.setPreviewOutcallObClidGroup(dto.getPreview_outcall_ob_clid_group());
        
        setStaffAndGroupCode(info);
    }

    private void setStaffAndGroupCode(ConPhoneCallInfo info) {
        String cdrCno = getCdrCno(info);
        if (StringUtils.isBlank(info.getStaffId()) && StringUtils.isNotBlank(cdrCno)) {
            List<InAuthCrmStaff> staffs = inAuthCrmStaffMapper.queryStaffByCno(cdrCno);
            if (CollectionUtils.isNotEmpty(staffs)) {
                InAuthCrmStaff firstStaff = staffs.get(0);
                info.setStaffId(String.valueOf(firstStaff.getId()));
                info.setGroupCode(String.valueOf(firstStaff.getGroupCode()));
            }
        }
    }

    // 提取公共方法
    private String getCdrCno(ConPhoneCallInfo info) {
        if ("4".equals(info.getCdrCallType())) {
            return info.getCdrCno();
        } else if ("1".equals(info.getCdrCallType())) {
            return info.getCdrCalleeCno();
        }
        return null;
    }


    private void insertOrUpdate(ConPhoneCallInfo info) {
        if (Objects.isNull(info.getId())) {
            conPhoneCallInfoMapper.insert(info);
        } else {
            info.setGmtModify(new Date());
            conPhoneCallInfoMapper.updateById(info);
        }
    }

    @Override
    public List<PhoneRecordVO> queryRecord(String queryDate) {
        if (!isValid(queryDate)) {
            throw new FastRuntimeException("时间格式异常,请输入 yyyy-MM-dd 格式");
        }
        List<PhoneRecordVO> list = conPhoneCallInfoMapper.queryPhoneRecord(queryDate, queryDate + " 23:59:59");
        // enterpriseId和对应的token的map
        Map<String, String> enterpriseIdTokenMap = BusiConstant.ENTERPRISE_IDS.stream()
            .collect(Collectors.toMap(s -> s, s -> trTokenService.getTokenByEnterpriseId(s)));

        list.forEach(item -> item.setSign(enterpriseIdTokenMap.get(item.getEnterpriseId())));
        return list;
    }

    private boolean isValid(String queryDate) {
        SimpleDateFormat sdf = new SimpleDateFormat(com.welab.common.utils.DateUtil.TimeFormatter.YYYY_MM_DD);
        sdf.setLenient(false);
        try {
            sdf.parse(queryDate);
        } catch (ParseException e) {
            return false;
        }
        return true;
    }
}
