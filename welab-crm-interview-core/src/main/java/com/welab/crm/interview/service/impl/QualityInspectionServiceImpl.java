package com.welab.crm.interview.service.impl;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.welab.common.utils.HttpClientUtil;
import com.welab.common.utils.HttpConfig;
import com.welab.common.utils.HttpHeader;
import com.welab.crm.interview.domain.ConPhoneCallInfo;
import com.welab.crm.interview.dto.QualityInspectionCallbackDTO;
import com.welab.crm.interview.dto.QualityInspectionRequestDTO;
import com.welab.crm.interview.dto.QualityInspectionResultDTO;
import com.welab.crm.interview.mapper.ConPhoneCallInfoMapper;
import com.welab.crm.interview.service.QualityInspectionService;
import com.welab.crm.interview.service.TrTokenService;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.scheduling.annotation.Async;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import javax.annotation.Resource;
import java.util.Date;
import java.util.Objects;

/**
 * 质检服务实现类
 * <AUTHOR> Generated
 * @date 2024-08-04
 */
@Service
@Slf4j
public class QualityInspectionServiceImpl implements QualityInspectionService {

    @Resource
    private ConPhoneCallInfoMapper conPhoneCallInfoMapper;

    @Resource
    private TrTokenService trTokenService;

    @Value("${quality.inspection.api.base.url:}")
    private String qualityInspectionApiBaseUrl;

    @Value("${quality.inspection.callback.url:}")
    private String qualityInspectionCallbackUrl;

    @Value("${quality.inspection.timeout:30000}")
    private int qualityInspectionTimeout;

    private static final String SUBMIT_API_PATH = "/api/v1/quality/submit";
    private static final String RESULT_API_PATH = "/api/v1/quality/result";

    @Override
    public String submitQualityInspection(QualityInspectionRequestDTO requestDTO) {
        try {
            String url = qualityInspectionApiBaseUrl + SUBMIT_API_PATH;
            
            JSONObject params = new JSONObject();
            params.put("recordingUrl", requestDTO.getRecordingUrl());
            params.put("callId", requestDTO.getCallId());
            params.put("callType", requestDTO.getCallType());
            params.put("customerNumber", requestDTO.getCustomerNumber());
            params.put("agentNumber", requestDTO.getAgentNumber());
            params.put("callDuration", requestDTO.getCallDuration());
            params.put("callbackUrl", qualityInspectionCallbackUrl);

            HttpConfig httpConfig = HttpConfig.custom()
                    .url(url)
                    .json(JSON.toJSONString(params))
                    .headers(HttpHeader.custom().contentType(HttpHeader.Headers.APPLICATION_JSON).build())
                    .requestTimeout(qualityInspectionTimeout)
                    .connectTimeout(qualityInspectionTimeout)
                    .socketTimeout(qualityInspectionTimeout);

            log.info("提交质检任务，参数：{}", JSON.toJSONString(params));
            String response = HttpClientUtil.post(httpConfig);
            log.info("质检任务提交响应：{}", response);

            JSONObject responseJson = JSON.parseObject(response);
            if (responseJson.getInteger("code") == 200) {
                return responseJson.getJSONObject("data").getString("taskId");
            } else {
                log.error("提交质检任务失败：{}", response);
                return null;
            }
        } catch (Exception e) {
            log.error("提交质检任务异常", e);
            return null;
        }
    }

    @Override
    public QualityInspectionResultDTO getQualityInspectionResult(String taskId) {
        try {
            String url = qualityInspectionApiBaseUrl + RESULT_API_PATH + "?taskId=" + taskId;
            
            HttpConfig httpConfig = HttpConfig.custom()
                    .url(url)
                    .headers(HttpHeader.custom().contentType(HttpHeader.Headers.APPLICATION_JSON).build())
                    .requestTimeout(qualityInspectionTimeout)
                    .connectTimeout(qualityInspectionTimeout)
                    .socketTimeout(qualityInspectionTimeout);

            log.info("获取质检结果，任务ID：{}", taskId);
            String response = HttpClientUtil.get(httpConfig);
            log.info("质检结果响应：{}", response);

            JSONObject responseJson = JSON.parseObject(response);
            if (responseJson.getInteger("code") == 200) {
                return JSON.parseObject(responseJson.getJSONObject("data").toJSONString(), 
                                      QualityInspectionResultDTO.class);
            } else {
                log.error("获取质检结果失败：{}", response);
                return null;
            }
        } catch (Exception e) {
            log.error("获取质检结果异常", e);
            return null;
        }
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void handleQualityInspectionCallback(QualityInspectionCallbackDTO callbackDTO) {
        log.info("处理质检回调，参数：{}", JSON.toJSONString(callbackDTO));
        
        try {
            // 根据任务ID查找对应的通话记录
            LambdaQueryWrapper<ConPhoneCallInfo> queryWrapper = Wrappers.lambdaQuery(ConPhoneCallInfo.class)
                    .eq(ConPhoneCallInfo::getQualityInspectionTaskId, callbackDTO.getTaskId());
            
            ConPhoneCallInfo phoneCallInfo = conPhoneCallInfoMapper.selectOne(queryWrapper);
            if (Objects.isNull(phoneCallInfo)) {
                log.warn("未找到对应的通话记录，任务ID：{}", callbackDTO.getTaskId());
                return;
            }

            // 获取详细的质检结果
            QualityInspectionResultDTO resultDTO = getQualityInspectionResult(callbackDTO.getTaskId());
            
            // 更新通话记录的质检信息
            if (callbackDTO.getStatus() == 1 && resultDTO != null) {
                // 质检成功
                phoneCallInfo.setQualityInspectionStatus(2); // 已完成
                phoneCallInfo.setQualityInspectionScore(resultDTO.getScore());
                phoneCallInfo.setQualityInspectionTime(new Date());
            } else {
                // 质检失败
                phoneCallInfo.setQualityInspectionStatus(3); // 检查失败
                phoneCallInfo.setQualityInspectionTime(new Date());
                log.error("质检失败，任务ID：{}，错误信息：{}", callbackDTO.getTaskId(), callbackDTO.getErrorMessage());
            }

            phoneCallInfo.setGmtModify(new Date());
            conPhoneCallInfoMapper.updateById(phoneCallInfo);
            
            log.info("质检回调处理完成，通话ID：{}，状态：{}", phoneCallInfo.getCdrMainUniqueId(), 
                    phoneCallInfo.getQualityInspectionStatus());
            
        } catch (Exception e) {
            log.error("处理质检回调异常", e);
            throw e;
        }
    }

    @Override
    @Async
    public void submitPhoneRecordQualityInspection(String cdrMainUniqueId, String recordFile, 
                                                 String callType, String customerNumber, String agentNumber) {
        log.info("异步提交电话录音质检，通话ID：{}，录音文件：{}", cdrMainUniqueId, recordFile);
        
        try {
            // 检查是否已经提交过质检
            LambdaQueryWrapper<ConPhoneCallInfo> queryWrapper = Wrappers.lambdaQuery(ConPhoneCallInfo.class)
                    .eq(ConPhoneCallInfo::getCdrMainUniqueId, cdrMainUniqueId);
            
            ConPhoneCallInfo phoneCallInfo = conPhoneCallInfoMapper.selectOne(queryWrapper);
            if (Objects.isNull(phoneCallInfo)) {
                log.warn("未找到对应的通话记录：{}", cdrMainUniqueId);
                return;
            }

            // 如果已经在质检中或已完成，则跳过
            if (phoneCallInfo.getQualityInspectionStatus() != null && phoneCallInfo.getQualityInspectionStatus() > 0) {
                log.info("通话记录已经提交质检或已完成，跳过：{}", cdrMainUniqueId);
                return;
            }

            // 获取录音文件URL
            String enterpriseId = recordFile.substring(0, 7);
            String recordingUrl = trTokenService.getWaveSoundUrl(enterpriseId, recordFile);
            
            if (StringUtils.isBlank(recordingUrl)) {
                log.warn("无法获取录音文件URL：{}", recordFile);
                return;
            }

            // 构建质检请求
            QualityInspectionRequestDTO requestDTO = new QualityInspectionRequestDTO();
            requestDTO.setRecordingUrl(recordingUrl);
            requestDTO.setCallId(cdrMainUniqueId);
            requestDTO.setCallType(callType);
            requestDTO.setCustomerNumber(customerNumber);
            requestDTO.setAgentNumber(agentNumber);
            requestDTO.setCallbackUrl(qualityInspectionCallbackUrl);

            // 提交质检任务
            String taskId = submitQualityInspection(requestDTO);
            
            if (StringUtils.isNotBlank(taskId)) {
                // 更新质检状态为检查中
                phoneCallInfo.setQualityInspectionStatus(1); // 检查中
                phoneCallInfo.setQualityInspectionTaskId(taskId);
                phoneCallInfo.setGmtModify(new Date());
                conPhoneCallInfoMapper.updateById(phoneCallInfo);
                
                log.info("质检任务提交成功，通话ID：{}，任务ID：{}", cdrMainUniqueId, taskId);
            } else {
                log.error("质检任务提交失败，通话ID：{}", cdrMainUniqueId);
            }
            
        } catch (Exception e) {
            log.error("异步提交电话录音质检异常，通话ID：{}", cdrMainUniqueId, e);
        }
    }
}
