<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.welab.crm.interview.mapper.ConPhoneCallInfoMapper">

    <!-- 通用查询映射结果 -->
    <resultMap id="BaseResultMap" type="com.welab.crm.interview.domain.ConPhoneCallInfo">
        <id column="id" property="id" />
        <result column="call_detail_guid" property="callDetailGuid" />
        <result column="staff_id" property="staffId" />
        <result column="group_code" property="groupCode" />
        <result column="cdr_enterprise_id" property="cdrEnterpriseId" />
        <result column="cdr_number_trunk" property="cdrNumberTrunk" />
        <result column="cdr_hotline" property="cdrHotline" />
        <result column="cdr_main_unique_id" property="cdrMainUniqueId" />
        <result column="cdr_customer_number" property="cdrCustomerNumber" />
        <result column="cdr_customer_area_code" property="cdrCustomerAreaCode" />
        <result column="cdr_customer_city" property="cdrCustomerCity" />
        <result column="cdr_customer_province" property="cdrCustomerProvince" />
        <result column="cdr_customer_number_type" property="cdrCustomerNumberType" />
        <result column="cdr_status" property="cdrStatus" />
        <result column="cdr_call_type" property="cdrCallType" />
        <result column="cdr_callee_cno" property="cdrCalleeCno" />
        <result column="cdr_join_queue_time" property="cdrJoinQueueTime" />
        <result column="cdr_bridge_time" property="cdrBridgeTime" />
        <result column="cdr_start_time" property="cdrStartTime" />
        <result column="cdr_answer_time" property="cdrAnswerTime" />
        <result column="cdr_end_time" property="cdrEndTime" />
        <result column="cdr_record_file" property="cdrRecordFile" />
        <result column="cdr_end_reason" property="cdrEndReason" />
        <result column="cdr_end_bridge_time" property="cdrEndBridgeTime" />
        <result column="cdr_queue" property="cdrQueue" />
        <result column="cdr_detail_sip_cause" property="cdrDetailSipCause" />
        <result column="cdr_investigation" property="cdrInvestigation" />
        <result column="cdr_cno" property="cdrCno" />
        <result column="cdr_x_number" property="cdrXNumber" />
        <result column="callee_ringing_time" property="calleeRingingTime" />
        <result column="cdr_request_unique_id" property="cdrRequestUniqueId" />
        <result column="cdr_agent_number" property="cdrAgentNumber" />
        <result column="cust_callee_clid" property="custCalleeClid" />
        <result column="cdr_clid" property="cdrClid" />
        <result column="gmt_create" property="gmtCreate" />
        <result column="gmt_modify" property="gmtModify" />
    </resultMap>

    <!-- 通用查询结果列 -->
    <sql id="Base_Column_List">
        id, call_detail_guid, staff_id, group_code, cdr_enterprise_id, cdr_number_trunk, cdr_hotline, cdr_main_unique_id, cdr_customer_number, cdr_customer_area_code, cdr_customer_city, cdr_customer_province, cdr_customer_number_type, cdr_status, cdr_call_type, cdr_callee_cno, cdr_join_queue_time, cdr_bridge_time, cdr_start_time, cdr_answer_time, cdr_end_time, cdr_record_file, cdr_end_reason, cdr_end_bridge_time, cdr_queue, cdr_detail_sip_cause, cdr_investigation, cdr_cno, cdr_x_number, callee_ringing_time, cdr_request_unique_id, cdr_agent_number, cust_callee_clid, cdr_clid, gmt_create, gmt_modify
    </sql>
    <select id="queryPhoneRecord" resultType="com.welab.collection.interview.vo.PhoneRecordVO">
        SELECT
        'TR' as provider,
        cdr_customer_number as mobile,
        cdr_start_time as startTime,
        cdr_end_time as endTime,
        (case when cdr_call_type = '1' then '呼入'  when cdr_call_type='4' then '呼出' end) as callType,
        (case when cdr_bridge_time is not null then '接听成功' else '接听失败' end) as callStatus,
        (case when cdr_call_type = '1' then cdr_callee_cno when cdr_call_type= '4' then cdr_cno end) as cno,
        iacs.staff_name as staffName,
        cdr_enterprise_id as enterpriseId,
        cdr_record_file as recordFile,
        'v10' as version,
        '6' as region,
        '客服' as enterpriseName
        from
        con_phone_call_info cpci
        left join in_auth_crm_staff iacs on cpci.staff_id = iacs.id
        where cdr_start_time between #{startDate} and #{endDate}
    </select>

</mapper>
