jdbc.driver=com.mysql.jdbc.Driver
#jdbc.url=***********************************************************************************************************************************
#jdbc.url=*************************************************************************************************************************************
#jdbc.url=****************************************************************************
#jdbc.username=root
#jdbc.password=mysql321

# fat
jdbc.url=******************************************************************************************************************************************************************
jdbc.username=db_public
jdbc.password=u#Lmmb&wgmr3U2S9

# dev
#jdbc.url=*****************************************************************************
#jdbc.username=welab_crm_dev
#jdbc.password=qRlrkMOH^sg8

#mybatis-plus\u914D\u7F6E\u63A7\u5236\u53F0\u6253\u5370\u5B8C\u6574\u5E26\u53C2\u6570SQL\u8BED\u53E5
#mybatis-plus.configuration.log-impl=org.apache.ibatis.logging.stdout.StdOutImpl

druid.filters=stat,wall
druid.connectionProperties=config.decrypt=true;config.decrypt.key=MFwwDQYJKoZIhvcNAQEBBQADSwAwSAJBAJv4CwvgYgsoHK+vsDlYfLyY9H6kUYB0UznYxrE4mF4eg8qwjMyG/N0PBhVbOFPlAD20Cg44hBegEeSjlf+DY7sCAwEAAQ==

#zookeeper.url=localhost:2181
#fat3
#zookeeper.url=**********:2181
#dev
#zookeeper.url=**********:2181
#fat
zookeeper.url=zk-fat.welab-inc.com:32181
#zookeeper.url=zk-dubbo01.service:2181,zk-dubbo02.service:2182

##job
job.registry.address=zk-fat.welab-inc.com:32181
job.sms.send.cron = 0 0/1 * * * ?
job.sms.confirm.cron = 0 0/3 * * * ?

#po package name 
table.package=com.welab.crm.interview.domain

#transaction aop execution
aop.services=execution(* com.welab.crm.interview.service.impl.*.*(..))

## Mybatis \u914D\u7F6E
mybatis.typeAliasesPackage=com.welab.crm.interview.domain
mybatis.mapperLocations=classpath:mapper/*.xml

#welabb-xdao async dao
#async.dao.core_pool_size=20
#async.dao.max_pool_size=200
#async.dao.queue_size=500

#mq\u76F8\u5173\u53C2\u6570
#welab.mq.uri=**********************************
#welab.mq.sender.queue=welab.message-wechat.activity.queue
#welab.mq.listener.queue=welab.message-wechat.activity.queue

#redis config
#redisType(required): single/sharded/cluster
redis.redisType=cluster
#address(required): host1:port1,host2:port2,host3:port3...
#single dev
#redis.address=**********:6379
#single fat
#redis.address=**********:6379
#cluster dev
#redis.address=**********:8001,**********:8002,**********:8003
#cluster fat
redis.address=redis-cluster-fat.welab-inc.com:32379
redis.password=devappwsx
management.health.redis.enabled = false

elite.user.token = eyJ0eXAiOiJKV1QiLCJhbGciOiJIUzI1NiJ9.**************************************************************************************************************************************************.ZQLkA7uTLZGsExpq6q3RiN6v-T2MHco5hhslTVoMVLc
usercenter.http.url = http://japi-fat.wolaidai.com

# oss
oss.prefix = system/documents

#Event mq
welab.event.mq.uri = amqp://welend:<EMAIL>:5672/welab
welab.event.mq.listener.enable = false
welab.event.mq.listener.queue = management-application.queue
welab.event.mq.prefetchCount = 10
welab.event.mq.publisher.enable = true
welab.event.mq.application.applied = management_application_applied_event

# fat \u77ED\u4FE1\u53D1\u9001\u63A5\u53E3
sms.url = https://marketing-fat.wolaidai.com/message-sms/api/v1
# \u77ED\u4FE1\u5E73\u53F0\u914D\u7F6E,\u5BA2\u670D\u7CFB\u7EDF\u9700\u4E0E\u77ED\u4FE1\u5E73\u53F0\u4E00\u81F4
sms.userId = kf_ghb
# \u77ED\u4FE1\u5E73\u53F0\u914D\u7F6E,\u5BA2\u670D\u7CFB\u7EDF\u9700\u4E0E\u77ED\u4FE1\u5E73\u53F0\u4E00\u81F4
sms.secretKey = abc123

## wechat url and key(encrypt)
welab.wechat.robot.push.url=https://qyapi.weixin.qq.com/cgi-bin/webhook/send
welab.wechat.robot.push.key=CG/DP3oiflskrvvEXjhxZg5y4+iOwgTK/fqLNyP68VbtfSXTctEnrCFFLE8BFWnO

usercenter.operate.id = 417

mobile.belongs.query.url = http://xexdata-fat.wolaidai.com/tools/mobile?account=

ai.sale.push.url = http://repay-fat.slb.cluster.coltd:8000/welab-voice-ivr/ivr/self_collection/sale/batchInput

#welab.privacy.root.url = https://japi-fat.slb.cluster.coltd:8000/privacy/api/v2/config-info
welab.privacy.root.url = https://japi-fat.wolaidai.com/privacy/api/v2/config-info
welab.privacy.secret.key = N2qevU18ctVFJjJV

job.withhold.sync.cron = 0 0/5 * * * ?
job.quota.sync.cron = 0 0/30 * * * ?

lender.zckb.url = http://welab-capital.service:8080/welab-capital/v1/capital/priority/productType/query
lender.zckb.code.url =  http://welab-capital.service:8080/welab-capital/v1/capital/channel/tabList

dubbo.provider.thread = 600
dubbo.provider.accepts = 300

lhbank.http.get.url = https://lender-fat.wolaidai.com/lhbank-investment/api/v1/user/get/settlement
job.settle.sync.cron = 0 0/30 * * * ?

sms.yx.userId = kfdx_ghb

pangu.verify.url = http://xexdata-fat.wolaidai.com/tools/selfMobileVerifyRealName

webot.face.url = https://cs-dev.wld.net/api/api/face
webot.face.userToken=df38a664-41a3-4631-8458-94321d5b67da
webot.face.appKey=aS5u68Ke


tr.kf.enterpriseId = 7600088

job.pull.ib.report.cron = 0 0 7 * * ?

video.check.app.key = hkv2y1tf
video.check.token = aa6j78dh-5sb7-xrfh-9vx8-ud331rzvoj9x
video.check.url = http://**********:19000/video/analysis
video.sp.url = https://mkt-fat.wld.net/web-static/activityTemplate/video/index.html
video.upload.path = /data/logs/file


spring.http.multipart.max-file-size=100MB
spring.http.multipart.max-request-size=100MB


job.refund.push.cron = 0 0 8 * * ?


online.system.app.key = aS5u68Ke
online.system.user.token = df38a664-41a3-4631-8458-94321d5b67da
online.system.http.url.pre = https://cs-dev.wld.net

job.h5repay.result.sync.cron = 0 2 8-22 * * ?

label.aes.key=KX4iSKSGXzotI69emwI+iw==
label.system.url=http://admin-fat.wolaidai.com/label-api-server/labelApplyUsing/search

welab.query.origin.name=http://admin-openresty-fat.slb.cluster.coltd:8000/loan-management-web-server/dataRelease/originInfo
welab.ca.bank.investment= http://lender-openresty-fat.slb.cluster.coltd:8000/cabank-investment/api/v1/quota/write/off

lender.http.pre = http://lender-fat.slb.cluster.coltd:8000
job.staff.monitor.warning.cron = 0 0/1 * * * ?
staff.monitor.warning.robot.key = 4c65ee4f-916d-49dc-9e17-f35c11d6bf2c
staff.work.order.monitor.warning.robot.key = 4c65ee4f-916d-49dc-9e17-f35c11d6bf2c
job.order.notice.daily.cron = 0 0 1 * * ?
job.order.notice.within.two.month.cron = 0 0 1 * * ?
job.order.notice.over.two.month.cron = 0 0 1 * * ?
bank.release.url = http://lender-openresty-fat.slb.cluster.coltd:8000/{0}/api/v1/quota/write/off
ai.summary.base.url = http://10.90.0.5:8888

# 质检API配置
quality.inspection.api.base.url = http://quality-inspection-api.service:8080
quality.inspection.callback.url = http://welab-crm-interview.service:8080/welab-crm-interview/v1/phone/quality/callback
quality.inspection.timeout = 30000

user.modify.name.original.url = https://m.wld.net/pub/rename/verify?channel=op_prm_oth_00000001
short.link.get.url = https://bkv2-fat.wld.net/d/v1/urls
short.link.expiration = 5


bairong.public.key = MIIBIjANBgkqhkiG9w0BAQEFAAOCAQ8AMIIBCgKCAQEA24AJGKk3d1O7JkcxlndviB8As2gbybcVyCvcD3FDnhd9hBXdod0yFBxidB8Wf5kdexJoGtUblDsQuzNYAxDHbEDGcmU9mLSmTYIhjAgOoaoEg7x6OsxBU1W2O6ahOri8oj8BlOVOVbtIy7qeXIHjH212FVEowwRSIIyoWLKjJAar9aa5gIMHOKTkNY/HedTDTvdFH2IjFbvCw/+LujcxwaMtUiVuej/r78ZjK0aDN6eu3tjmxtdJ7xiH+cd91xX/MoP1OQnPcJn9kS3w+fLPzs0scLMYRMEGu+BBVcMEurocct7Qij2CAuG5MT2GrNgV+1D+3r+Mq1Ty1dXH9GowTQIDAQAB
bairong.private.key = MIIEvgIBADANBgkqhkiG9w0BAQEFAASCBKgwggSkAgEAAoIBAQDbgAkYqTd3U7smRzGWd2+IHwCzaBvJtxXIK9wPcUOeF32EFd2h3TIUHGJ0HxZ/mR17Emga1RuUOxC7M1gDEMdsQMZyZT2YtKZNgiGMCA6hqgSDvHo6zEFTVbY7pqE6uLyiPwGU5U5Vu0jLup5cgeMfbXYVUSjDBFIgjKhYsqMkBqv1prmAgwc4pOQ1j8d51MNO90UfYiMVu8LD/4u6NzHBoy1SJW56P+vvxmMrRoM3p67e2ObG10nvGIf5x33XFf8yg/U5Cc9wmf2RLfD58s/OzSxwsxhEwQa74EFVwwS6uhxy3tCKPYIC4bkxPYas2BX7UP7ev4yrVPLV1cf0ajBNAgMBAAECggEAfVNmXjFH3RbutVVS59F72eGxub3Il+y0QB1iwWJvNBW/cao2aFPuYvMt95VEeEhJP9rrm95vp7vtuqT/LIjpBFMbu3c4qzcB+SFC3DxBdmanuzvjIi07Y0R+DyL9zZRrB8wnsAcZKfQMBV6hSrpSnsAV+ufi+S4/YhxNho9AfLFPpM3m6OI1RCAW8N6A44GRaKkR5m2fWQxTsOl6tPDnfg+RD8zOVcAcmlAcCggQIjNBqZ4G8Y+DWc4Ky1U4igtD8YvHoOJQwLOH9cZj2E2vctoVTC0Lz/9UXck7U292XWlRgF60k8dhnq7MqFKIC2Gwv197mESSOThICJ0+Jen8AQKBgQD1WUshz8SGLym/4bvDcl/0vDyndZjwMi/CK/EtSOMyb1m/sCYl64j7sCtV2/95yLmMZyztl/icTxVLfK+sL/l1Yqto6mtX/FR4lomzKatGBEchpHZKVW2Y6EQTEZR3Izrko3TSG2/dWmPQ2p7gjWU4Byd/P1j2K4aZ0PyjqfqvAQKBgQDlB3h1Y40kWoy5VZqan3hUsrdeg2bgBUxGq1PKqiRigIjavNZLoB9+bKiWu5jthLew/UKK+iIDJ5R7nnHqMPRIifninv4gTQiwCXpvnVPcg6EA13uFIL9dLjpOZyLgSQHZYUButhdnqM99TZn1w8LwOhD/4YPrrvlu9AzyYaCNTQKBgQDj5VR3jLEkXuyt0nUBX90nzC2e8wnjmwpnlz+H69s1/t5jwrA8quLrvsESGK7epYdMlo3SCEaikobaPnBDRbC2y7ciVhTtfnHMLTCs3fi8qb6UzloeQ+nwjVamS78XRv8uX3Jks52eA5cR6fCH7m7lsyeJ+AKfbuCxQcFaGMrFAQKBgQC6dtuWz0dBWsuJtO1biAwjASITu+3M4+NXAfQFY4KeA8HoPKhICAS8HGEh33hBoLUhTwCXP9DjOp1EDBr4rYSlblhZLlL+H1m8YOsi5jc5qJ0znzx8hc3nYlSsMoINksMDpY8c0/m4NpM/lCeOFSSoCs5+Va/e+aUwtVe5GszpXQKBgDVOxbW2Y708ZPVbAFPGfcsKjLYcSqAkn38Raeecs/IqbdwZ6AFI/puneuoxp5Rp2qq4nIwu/dQ64baBG5OOwdEFLRC2IR2kY5HFb/qBB7L1i0siUrh+tuVYRST36wUrupxiw03tq+BIrFKjbkubS6t9ORcZFgn+8C4SEB+F6IDZ

bairong.aes.key = bairong_

job.over.sevenday.uncontact.order.notice.cron = 0 0 14 * * ?
job.over.twentyfour.hour.uncontact.order.notice.cron = 0 0 10 * * ?

kf.send.contract.url = https://www.baidu.com
contract.url.expiration.minute = 5
job.response.timeout.state.update.cron = 0 0/10 * * * ?